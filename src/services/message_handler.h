#pragma once

#include <QObject>
#include <QString>

#include "chat_context.h"

namespace ChamberUI::Services {

class LLMMessageHandler {
public:
  virtual ~LLMMessageHandler() = default;
  virtual void onStreamFragment(const QString &message,
                                const ChatContext &context) = 0;
  virtual void onError(const QString &error, const ChatContext &context) = 0;
  virtual void onFinished(bool succeed, const ChatContext &context) = 0;
};

} // namespace ChamberUI::Services
