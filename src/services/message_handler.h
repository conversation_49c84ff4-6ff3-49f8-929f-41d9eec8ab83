#pragma once

#include <QObject>
#include <QString>

#include "chat_context.h"

namespace ChamberUI::Services {

class LLMMessageHandler {
public:
  virtual ~LLMMessageHandler() = default;
  virtual void onStreamFragment(const QString &message,
                                ChatContext &context) = 0;
  virtual void onError(const QString &error, ChatContext &context) = 0;
  virtual void onFinished(bool succeed, ChatContext &context) = 0;
};

} // namespace ChamberUI::Services
