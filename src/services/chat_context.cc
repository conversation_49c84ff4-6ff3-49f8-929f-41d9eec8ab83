#include "chat_context.h"

namespace ChamberUI::Services {

ChatMessage::ChatMessage(QString content, MessageRole role)
    : m_content{std::move(content)}, m_role{role} {}

ChatContext::ChatContext(const SessionConfig &config)
    : config_{config}, messages_{} {}

void ChatContext::onAssistantMessageFinished() {
  if (!pending_response_.isEmpty()) {
    addMessage(std::move(pending_response_), MessageRole::Assistant);
    pending_response_.clear();
  }
}
void ChatContext::clearUnfinishedAssistantMessage() {
  pending_response_ = "";
}

} // namespace ChamberUI::Services