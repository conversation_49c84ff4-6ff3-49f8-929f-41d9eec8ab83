#pragma once

#include "llm_provider_base.h"

namespace ChamberUI::Services {

class OpenAIProvider : public LLMProviderBase {

public:
  explicit OpenAIProvider(gsl::not_null<QObject *> parent)
      : LLMProviderBase(parent) {}
  ~OpenAIProvider() override = default;

  void sendMessage(const QString &message, ChatContext &context,
                   LLMMessageHandler &handler) override;

protected:
  void setAuthorization(QNetworkRequest &request,
                        const ChatContext &context) const override;
  void initRequestBody(QJsonObject &body, ChatContext &context) override;
  void onStreamFragment(const QByteArray &data, ChatContext &context,
                        LLMMessageHandler &handler) override;

private:
  static void onStreamLine(const QString &line, ChatContext &context,
                           LLMMessageHandler &handler);
};

} // namespace ChamberUI::Services