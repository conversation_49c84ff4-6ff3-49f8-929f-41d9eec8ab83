#pragma once

#include "llm_provider_base.h"

namespace ChamberUI::Services {

class OpenAIProvider : public LLMProviderBase {

public:
  ~OpenAIProvider() final = default;

  void sendMessage(const QString &message, MessageRole role,
                   ChatContext &context, LLMMessageHandler &handler) override;

protected:
  void setAuthorization(QNetworkRequest &request,
                        const ChatContext &context) const override;
  void initRequestBody(QJsonObject &body, const ChatContext &context) override;
  void onStreamFragment(const QByteArray &data, const ChatContext &context,
                        LLMMessageHandler &handler) const override;
  void onStreamLine(const QString &line, const ChatContext &context,
                    LLMMessageHandler &handler) const;
};

} // namespace ChamberUI::Services