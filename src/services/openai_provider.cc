#include "openai_provider.h"

#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkReply>
#include <QNetworkRequest>

#include "message_handler.h"

namespace ChamberUI::Services {

void OpenAIProvider::sendMessage(const QString &message, ChatContext &context,
                                 LLMMessageHandler &handler) {

  context.addUserMessage(message);

  QJsonObject body{};
  initRequestBody(body, context);
  QJsonDocument doc{body};

  auto request = createRequest(context);
  postRequest(*request, doc.toJson(), context, handler);
}

void OpenAIProvider::setAuthorization(QNetworkRequest &request,
                                      const ChatContext &context) const {
  request.setRawHeader("Authorization",
                       ("Bearer " + context.config().api_key).toUtf8());
}

void OpenAIProvider::initRequestBody(QJsonObject &body, ChatContext &context) {
  QJsonArray messages{};
  for (const auto &msg : context.messages()) {
    if (msg->role() != MessageRole::Assistant &&
        msg->role() != MessageRole::User) {
      continue;
    }
    QJsonObject json_msg{};
    json_msg["role"] = msg->role() == MessageRole::User ? "user" : "assistant";
    json_msg["content"] = msg->content();
    messages.append(json_msg);
  };

  QJsonObject requestBody;
  requestBody["model"] = context.config().model;
  requestBody["messages"] = messages;
  requestBody["stream"] = true;
  requestBody["temperature"] = context.config().temperature;
  requestBody["max_tokens"] = context.config().max_tokens;
}

void OpenAIProvider::onStreamFragment(const QByteArray &data,
                                      ChatContext &context,
                                      LLMMessageHandler &handler) {
  QString dataString = QString::fromUtf8(data);
  QStringList lines = dataString.split('\n', Qt::SkipEmptyParts);

  for (const QString &line : lines) {
    if (line.startsWith("data: ")) {
      onStreamLine(line.mid(6), context, handler); // Remove "data: " prefix
    }
  }
}

void OpenAIProvider::onStreamLine(const QString &line, ChatContext &context,
                                  LLMMessageHandler &handler) {

  if (line.trimmed() == "[DONE]") {
    return;
  }

  QJsonParseError error;
  QJsonDocument doc = QJsonDocument::fromJson(line.toUtf8(), &error);

  if (error.error != QJsonParseError::NoError) {
    // TODO better error handling
    handler.onError("Invalid JSON received", context);
    return; // Skip invalid JSON
  }

  QJsonObject obj = doc.object();
  QJsonArray choices = obj["choices"].toArray();

  if (!choices.isEmpty()) {
    QJsonObject choice = choices[0].toObject();
    QJsonObject delta = choice["delta"].toObject();

    if (delta.contains("content")) {
      QString content = delta["content"].toString();
      if (!content.isEmpty()) {
        handler.onStreamFragment(content, context);
      }
    }
  }
}

} // namespace ChamberUI::Services
