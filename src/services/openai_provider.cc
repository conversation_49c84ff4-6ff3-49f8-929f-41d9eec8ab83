#include "openai_provider.h"

#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkReply>
#include <QNetworkRequest>

#include "message_handler.h"

namespace ChamberUI::Services {

void OpenAIProvider::sendMessage(const QString &message, MessageRole role,
                                 ChatContext &context,
                                 LLMMessageHandler &handler) {
  auto request = createRequest(context);
  QJsonObject body{};
  initRequestBody(body, context);
  QJsonDocument doc{body};
  postRequest(*request, doc.toJson(), context, handler);
}

void OpenAIProvider::setAuthorization(QNetworkRequest &request,
                                      const ChatContext &context) const {
  request.setRawHeader("Authorization",
                       ("Bearer " + context.config().api_key).toUtf8());
}

void OpenAIProvider::initRequestBody(QJsonObject &body,
                                     const ChatContext &context) {}

void OpenAIProvider::onStreamFragment(const QByteArray &data,
                                      const ChatContext &context,
                                      LLMMessageHandler &handler) const {
  QString dataString = QString::fromUtf8(data);
  QStringList lines = dataString.split('\n', Qt::SkipEmptyParts);

  for (const QString &line : lines) {
    if (line.startsWith("data: ")) {
      onStreamLine(line.mid(6), context, handler); // Remove "data: " prefix
    }
  }
}

void OpenAIProvider::onStreamLine(const QString &line,
                                  const ChatContext &context,
                                  LLMMessageHandler &handler) const {

  if (line.trimmed() == "[DONE]") {
    return;
  }

  QJsonParseError error;
  QJsonDocument doc = QJsonDocument::fromJson(line.toUtf8(), &error);

  if (error.error != QJsonParseError::NoError) {
    // TODO better error handling
    handler.onError("Invalid JSON received", context);
    return; // Skip invalid JSON
  }

  QJsonObject obj = doc.object();
  QJsonArray choices = obj["choices"].toArray();

  if (!choices.isEmpty()) {
    QJsonObject choice = choices[0].toObject();
    QJsonObject delta = choice["delta"].toObject();

    if (delta.contains("content")) {
      QString content = delta["content"].toString();
      if (!content.isEmpty()) {
        handler.onStreamFragment(content, context);
      }
    }
  }
}

} // namespace ChamberUI::Services
