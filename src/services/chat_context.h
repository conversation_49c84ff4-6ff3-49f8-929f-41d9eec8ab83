#pragma once

#include <list>
#include <memory>

#include <QString>

#include "session_config.h"

namespace ChamberUI::Services {

enum class MessageRole { None, User, Assistant, System };

class ChatMessage {
public:
  ChatMessage(QString content, MessageRole role);
  [[nodiscard]] const QString &content() const { return m_content; }
  [[nodiscard]] MessageRole role() const { return m_role; }

private:
  const QString m_content;
  const MessageRole m_role;
};

using ChatMessageConstPtr = std::unique_ptr<const ChatMessage>;

class ChatContext {
public:
  explicit ChatContext(const SessionConfig &config);
  ~ChatContext() = default;

  // Delete copy constructor and assignment operator
  ChatContext(const ChatContext &) = delete;
  ChatContext &operator=(const ChatContext &) = delete;

  // Allow move constructor but delete move assignment operator (due to const
  // reference member)
  ChatContext(ChatContext &&) = default;
  ChatContext &operator=(ChatContext &&) = delete;

  void addMessage(QString content, MessageRole role) {
    messages_.emplace_back(new ChatMessage(std::move(content), role));
  }

  void addUserMessage(QString content) {
    addMessage(std::move(content), MessageRole::User);
  }

  void appendAssistantMessage(QString content) { pending_response_ += content; }

  void onAssistantMessageFinished();
  void clearUnfinishedAssistantMessage();

  [[nodiscard]] const std::list<ChatMessageConstPtr> &messages() const {
    return messages_;
  }

  [[nodiscard]] const SessionConfig &config() const { return config_; }

private:
  const SessionConfig &config_;
  std::list<ChatMessageConstPtr> messages_;
  QString pending_response_;
};

} // namespace ChamberUI::Services