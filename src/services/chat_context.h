#pragma once

#include <list>
#include <memory>

#include <QString>

#include "session_config.h"

namespace ChamberUI::Services {

enum class MessageRole { None, User, Assistant, System };

class ChatMessage {
public:
  ChatMessage(QString content, MessageRole role);
  [[nodiscard]] const QString &content() const { return m_content; }
  [[nodiscard]] MessageRole role() const { return m_role; }

private:
  const QString m_content;
  const MessageRole m_role;
};

using ChatMessageConstPtr = std::unique_ptr<const ChatMessage>;

class ChatContext {
public:
  explicit ChatContext(const SessionConfig &config);
  ~ChatContext() = default;

  void addMessage(QString content, MessageRole role) {
    messages_.emplace_back(new ChatMessage(std::move(content), role));
  }

  [[nodiscard]] const std::list<ChatMessageConstPtr> &messages() const {
    return messages_;
  }

  [[nodiscard]] const SessionConfig &config() const { return config_; }

private:
  const SessionConfig &config_;
  std::list<ChatMessageConstPtr> messages_;
};

} // namespace ChamberUI::Services