#include "llm_provider_base.h"

#include <QStringList>

#include "chat_context.h"
#include "message_handler.h"

namespace ChamberUI::Services {

LLMProviderBase::LLMProviderBase(gsl::not_null<QObject *> parent)
    : QObject{parent}, network_manager_{new QNetworkAccessManager(this)} {}

std::unique_ptr<QNetworkRequest>
LLMProviderBase::createRequest(const ChatContext &context) const {
  std::unique_ptr<QNetworkRequest> request =
      std::make_unique<QNetworkRequest>(context.config().api_url);
  request->setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
  request->setHeader(QNetworkRequest::UserAgentHeader, "LLMChatBot/1.0");
  setAuthorization(*request, context);
  return request;
}

void LLMProviderBase::onStreamFinished(bool succeed, const QString &message,
                                       ChatContext &context,
                                       LLMMessageHandler &handler) {
  if (succeed) {
    context.onAssistantMessageFinished();
    handler.onFinished(true, context);
  } else {
    context.clearUnfinishedAssistantMessage();
    handler.onError(message, context);
    handler.onFinished(false, context);
  }
}

void LLMProviderBase::postRequest(const QNetworkRequest &request,
                                  const QByteArray &data, ChatContext &context,
                                  LLMMessageHandler &handler) {
  QNetworkReply *reply = network_manager_->post(request, data);
  bindMessageHandler(reply, context, handler);
}

void LLMProviderBase::bindMessageHandler(QNetworkReply *reply,
                                         ChatContext &context,
                                         LLMMessageHandler &handler) {

  connect(reply, &QNetworkReply::readyRead, this,
          [&]() { onStreamFragment(reply->readAll(), context, handler); });

  connect(reply, &QNetworkReply::finished, this, [&]() {
    if (reply->error() != QNetworkReply::NoError) {
      onStreamFinished(false, reply->errorString(), context, handler);
    } else {
      onStreamFinished(true, "", context, handler);
    }
  });
}

} // namespace ChamberUI::Services
