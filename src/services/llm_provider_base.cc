#include "llm_provider_base.h"

#include <QStringList>

#include "chat_context.h"
#include "message_handler.h"

namespace ChamberUI::Services {

LLMProviderBase::LLMProviderBase(gsl::not_null<QObject *> parent)
    : QObject{parent}, network_manager_{new QNetworkAccessManager(this)} {}

std::unique_ptr<QNetworkRequest>
LLMProviderBase::createRequest(const ChatContext &context) const {
  std::unique_ptr<QNetworkRequest> request =
      std::make_unique<QNetworkRequest>(context.config().api_url);
  request->setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
  request->setHeader(QNetworkRequest::UserAgentHeader, "LLMChatBot/1.0");
  setAuthorization(*request, context);
  return request;
}

void LLMProviderBase::postRequest(const QNetworkRequest &request,
                                  const QByteArray &data,
                                  const Chat<PERSON>ontext &context,
                                  LLMMessageHandler &handler) const {
  QNetworkReply *reply = network_manager_->post(request, data);
  bindMessageHandler(reply, context, handler);
}

void LLMProviderBase::bindMessageHandler(QNetworkReply *reply,
                                         const ChatContext &context,
                                         LLMMessageHandler &handler) const {

  connect(reply, &QNetworkReply::readyRead, this,
          [&]() { onStreamFragment(reply->readAll(), context, handler); });

  connect(reply, &QNetworkReply::finished, this, [&]() {
    if (reply->error() != QNetworkReply::NoError) {
      handler.onError(reply->errorString(), context);
      handler.onFinished(false, context);
    } else {
      handler.onFinished(true, context);
    }
  });
}

} // namespace ChamberUI::Services
