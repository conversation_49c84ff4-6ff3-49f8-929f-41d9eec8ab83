#pragma once

#include <gsl/gsl>

#include <QMap>
#include <QObject>
#include <QString>

#include "../models/Types.h"
#include "llm_provider.h"

namespace ChamberUI::Services {
using namespace ChamberUI::Models;

class ChatService : public QObject {

public:
  void processMessage(ChatMessage &message);

signals:
  void responseReceived(const QString &sessionId, const QString &response);
  void errorOccurred(const QString &sessionId, const QString &error);
  void processingStarted(const QString &sessionId);
  void processingFinished(const QString &sessionId);

private:
  QMap<QString, gsl::not_null<LLMProvider *>> m_providers;
};

} // namespace ChamberUI::Services
