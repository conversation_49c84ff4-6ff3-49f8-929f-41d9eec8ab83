#pragma once

#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QObject>
#include <gsl/gsl>

#include "chat_context.h"
#include "llm_provider.h"

namespace ChamberUI::Services {

class LLMProviderBase : public LLMProvider, public QObject {

public:
  explicit LLMProviderBase(gsl::not_null<QObject *> parent);

protected:
  virtual void setAuthorization(QNetworkRequest &request,
                                const ChatContext &context) const = 0;
  [[nodiscard]]
  std::unique_ptr<QNetworkRequest>
  createRequest(const ChatContext &context) const;

  virtual void initRequestBody(QJsonObject &body,
                               const ChatContext &context) = 0;

  virtual void onStreamFragment(const QByteArray &data,
                                const ChatContext &context,
                                LLMMessageHandler &handler) const = 0;

  void postRequest(const QNetworkRequest &request, const QByteArray &data,
                   const ChatContext &context,
                   LLMMessageHandler &handler) const;

private:
  void bindMessageHandler(QNetworkReply *reply, const ChatContext &context,
                          LLMMessageHandler &handler) const;

private:
  QNetworkAccessManager *network_manager_;
};

} // namespace ChamberUI::Services
