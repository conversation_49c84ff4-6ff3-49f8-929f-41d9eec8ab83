#include "ChatSession.h"

namespace ChamberUI::Models {

ChatSession::ChatSession(QObject *parent)
    : QObject(parent), m_title("New Chat"),
      m_createdAt(QDateTime::currentDateTime()),
      m_lastActivity(QDateTime::currentDateTime()) {}

ChatSession::ChatSession(const QString &title, QObject *parent)
    : QObject(parent), m_title(title),
      m_createdAt(QDateTime::currentDateTime()),
      m_lastActivity(QDateTime::currentDateTime()) {}

void ChatSession::setTitle(const QString &title) { m_title = title; }

void ChatSession::setCreatedAt(const QDateTime &createdAt) {
  m_createdAt = createdAt;
}

void ChatSession::setLastActivity(const QDateTime &lastActivity) {
  m_lastActivity = lastActivity;
}

QQmlListProperty<ChatMessage> ChatSession::messages() {
  return QQmlListProperty<ChatMessage>(
      this, &m_messagesList, &ChatSession::appendMessage,
      &ChatSession::messageCount, &ChatSession::messageAt,
      &ChatSession::clearMessages);
}

void ChatSession::addMessage(ChatMessage *message) {
  if (!message)
    return;

  message->setParent(this);
  m_messagesList.append(message);
  updateLastActivity();
  emit messagesChanged();
  emit messageCountChanged();
}

void ChatSession::removeMessage(ChatMessage *message) {
  if (m_messagesList.removeOne(message)) {
    message->deleteLater();
    emit messagesChanged();
    emit messageCountChanged();
  }
}

void ChatSession::clearMessages() {
  qDeleteAll(m_messagesList);
  m_messagesList.clear();
  emit messagesChanged();
  emit messageCountChanged();
}

ChatMessage *ChatSession::messageAt(int index) const {
  if (index >= 0 && index < m_messagesList.size()) {
    return m_messagesList.at(index);
  }
  return nullptr;
}

void ChatSession::updateLastActivity() {
  setLastActivity(QDateTime::currentDateTime());
}

QString ChatSession::getPreviewText() const {
  if (m_messagesList.isEmpty()) {
    return "No messages";
  }

  auto lastMessage = m_messagesList.last();
  QString preview = lastMessage->content();
  if (preview.length() > 50) {
    preview = preview.left(47) + "...";
  }
  return preview;
}

// Static list property helpers
void ChatSession::appendMessage(QQmlListProperty<ChatMessage> *list,
                                ChatMessage *message) {
  auto session = qobject_cast<ChatSession *>(list->object);
  if (session) {
    session->addMessage(message);
  }
}

qsizetype ChatSession::messageCount(QQmlListProperty<ChatMessage> *list) {
  auto session = qobject_cast<ChatSession *>(list->object);
  return session ? session->m_messagesList.size() : 0;
}

ChatMessage *ChatSession::messageAt(QQmlListProperty<ChatMessage> *list,
                                    qsizetype index) {
  auto session = qobject_cast<ChatSession *>(list->object);
  return session ? session->messageAt(static_cast<int>(index)) : nullptr;
}

void ChatSession::clearMessages(QQmlListProperty<ChatMessage> *list) {
  auto session = qobject_cast<ChatSession *>(list->object);
  if (session) {
    session->clearMessages();
  }
}

} // namespace ChamberUI::Models