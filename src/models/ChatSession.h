#pragma once

#include "ChatMessage.h"

#include <QDateTime>
#include <QList>
#include <QObject>
#include <QProperty>
#include <QQmlEngine>
#include <QQmlListProperty>
#include <QString>

namespace ChamberUI::Models {

class ChatSession : public QObject {
  Q_OBJECT
  QML_ELEMENT

  Q_PROPERTY(QString title READ title WRITE setTitle NOTIFY titleChanged
                 BINDABLE bindableTitle)
  Q_PROPERTY(QDateTime createdAt READ createdAt WRITE setCreatedAt NOTIFY
                 createdAtChanged BINDABLE bindableCreatedAt)
  Q_PROPERTY(QDateTime lastActivity READ lastActivity WRITE setLastActivity
                 NOTIFY lastActivityChanged BINDABLE bindableLastActivity)
  Q_PROPERTY(QQmlListProperty<ChatMessage> messages READ messages NOTIFY
                 messagesChanged)
  Q_PROPERTY(int messageCount READ messageCount NOTIFY messageCountChanged)

public:
  explicit ChatSession(QObject *parent = nullptr);
  ChatSession(const QString &title, QObject *parent = nullptr);

  // Property getters
  QString title() const { return m_title; }
  QDateTime createdAt() const { return m_createdAt; }
  QDateTime lastActivity() const { return m_lastActivity; }
  int messageCount() const { return m_messagesList.size(); }

  // Property setters
  void setTitle(const QString &title);
  void setCreatedAt(const QDateTime &createdAt);
  void setLastActivity(const QDateTime &lastActivity);

  // Bindable properties
  QBindable<QString> bindableTitle() { return &m_title; }
  QBindable<QDateTime> bindableCreatedAt() { return &m_createdAt; }
  QBindable<QDateTime> bindableLastActivity() { return &m_lastActivity; }

  // Messages management
  QQmlListProperty<ChatMessage> messages();
  void addMessage(ChatMessage *message);
  void removeMessage(ChatMessage *message);
  void clearMessages();
  ChatMessage *messageAt(int index) const;

  // Utility methods
  Q_INVOKABLE void updateLastActivity();
  Q_INVOKABLE QString getPreviewText() const;

signals:
  void titleChanged();
  void createdAtChanged();
  void lastActivityChanged();
  void messagesChanged();
  void messageCountChanged();

private:
  // List property helpers
  static void appendMessage(QQmlListProperty<ChatMessage> *list,
                            ChatMessage *message);
  static qsizetype messageCount(QQmlListProperty<ChatMessage> *list);
  static ChatMessage *messageAt(QQmlListProperty<ChatMessage> *list,
                                qsizetype index);
  static void clearMessages(QQmlListProperty<ChatMessage> *list);

  Q_OBJECT_BINDABLE_PROPERTY(ChatSession, QString, m_title,
                             &ChatSession::titleChanged)
  Q_OBJECT_BINDABLE_PROPERTY(ChatSession, QDateTime, m_createdAt,
                             &ChatSession::createdAtChanged)
  Q_OBJECT_BINDABLE_PROPERTY(ChatSession, QDateTime, m_lastActivity,
                             &ChatSession::lastActivityChanged)

  QList<ChatMessage *> m_messagesList;
};

} // namespace ChamberUI::Models