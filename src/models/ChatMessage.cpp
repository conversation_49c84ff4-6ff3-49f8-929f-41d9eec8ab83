#include "ChatMessage.h"

#include <QUuid>

namespace ChamberUI::Models {

ChatMessage::ChatMessage(QObject *parent)
    : QObject{parent}, m_id{QUuid::createUuid().toString()}, m_sessionId{""},
      m_content{""}, m_isUser{false}, m_timestamp{QDateTime::currentDateTime()},
      m_status{MessageStatus::Received} {}

ChatMessage::ChatMessage(const QString &content, bool isUser, QObject *parent)
    : QObject{parent}, m_id{QUuid::createUuid().toString()}, m_sessionId{""},
      m_content{content}, m_isUser{isUser},
      m_timestamp{QDateTime::currentDateTime()},
      m_status{isUser ? MessageStatus::Sending : MessageStatus::Received} {}

} // namespace ChamberUI::Models