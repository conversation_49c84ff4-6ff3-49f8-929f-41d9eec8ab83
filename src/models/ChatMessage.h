#pragma once

#include <gsl/gsl>

#include <QDateTime>
#include <QObject>
#include <QProperty>
#include <QQmlEngine>
#include <QString>

namespace ChamberUI::Models {

class ChatMessage : public QObject {
  Q_OBJECT
  QML_ELEMENT

  Q_PROPERTY(QString id READ id WRITE setId BINDABLE bindableId)
  Q_PROPERTY(QString sessionId READ sessionId WRITE setSessionId BINDABLE
                 bindableSessionId)
  Q_PROPERTY(QString content READ content WRITE setContent NOTIFY contentChanged
                 BINDABLE bindableContent)
  Q_PROPERTY(bool isUser READ isUser WRITE setIsUser NOTIFY isUserChanged
                 BINDABLE bindableIsUser)
  Q_PROPERTY(QDateTime timestamp READ timestamp WRITE setTimestamp NOTIFY
                 timestampChanged BINDABLE bindableTimestamp)
  Q_PROPERTY(MessageStatus status READ status WRITE setStatus NOTIFY
                 statusChanged BINDABLE bindableStatus)

public:
  enum Role { None, User, Assistant, System };
  enum class MessageStatus { Sending, Sent, Received, Error };
  Q_ENUM(Role)
  Q_ENUM(MessageStatus)

  explicit ChatMessage(QObject *parent = nullptr);
  ChatMessage(const QString &content, bool isUser, QObject *parent = nullptr);

  // Property getters
  QString id() const { return m_id.value(); }
  QString sessionId() const { return m_sessionId.value(); }
  QString content() const { return m_content.value(); }
  Role role() const { return m_role.value(); }
  bool isUser() const { return m_isUser.value(); }
  QDateTime timestamp() const { return m_timestamp.value(); }
  MessageStatus status() const { return m_status.value(); }

  // Property setters
  void setId(const QString &id) { m_id = id; }
  void setSessionId(const QString &sessionId) { m_sessionId = sessionId; }
  void setContent(const QString &content) { m_content = content; }
  void setRole(Role role) { m_role = role; }
  void setIsUser(bool isUser) { m_isUser = isUser; }
  void setTimestamp(const QDateTime &timestamp) { m_timestamp = timestamp; }
  void setStatus(MessageStatus status) { m_status = status; }

  // Bindable properties
  QBindable<QString> bindableId() { return {&m_id}; }
  QBindable<QString> bindableSessionId() { return {&m_sessionId}; }
  QBindable<QString> bindableContent() { return {&m_content}; }
  QBindable<bool> bindableIsUser() { return {&m_isUser}; }
  QBindable<QDateTime> bindableTimestamp() { return {&m_timestamp}; }
  QBindable<MessageStatus> bindableStatus() { return {&m_status}; }

  Q_INVOKABLE QString roleString() const {
    switch (m_role.value()) {
    case User:
      return "User";
    case Assistant:
      return "Assistant";
    case System:
      return "System";
    default:
      return "None";
    }
  }

signals:
  void contentChanged();
  void isUserChanged();
  void timestampChanged();
  void statusChanged();

private:
  QProperty<QString> m_id;
  QProperty<QString> m_sessionId;
  QProperty<Role> m_role{None};
  Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, QString, m_content,
                             &ChatMessage::contentChanged)
  Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, bool, m_isUser,
                             &ChatMessage::isUserChanged)
  Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, QDateTime, m_timestamp,
                             &ChatMessage::timestampChanged)
  Q_OBJECT_BINDABLE_PROPERTY(ChatMessage, MessageStatus, m_status,
                             &ChatMessage::statusChanged)
};

} // namespace ChamberUI::Models