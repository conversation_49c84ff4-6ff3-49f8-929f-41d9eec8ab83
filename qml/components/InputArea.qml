import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../styles"

Rectangle {
    id: root
    
    property alias text: textInput.text
    property alias placeholderText: placeholder.text
    property bool canSend: false

    signal sendRequested()
    signal inputTextChanged()
    
    height: Math.max(48, Math.min(120, textInput.implicitHeight + 24))
    color: Colors.inputBackground
    border.color: textInput.activeFocus ? Colors.inputFocusBorder : Colors.inputBorder
    border.width: 1
    radius: 12
    
    Behavior on height {
        NumberAnimation { duration: 150; easing.type: Easing.OutCubic }
    }
    
    Behavior on border.color {
        ColorAnimation { duration: 150 }
    }
    
    RowLayout {
        anchors.fill: parent
        anchors.margins: 12
        spacing: 12
        
        // Attachment button (placeholder)
        Button {
            Layout.preferredWidth: 32
            Layout.preferredHeight: 32
            
            background: Rectangle {
                color: parent.hovered ? Colors.hoverBackground : "transparent"
                radius: 6
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
            
            contentItem: Text {
                text: "📎"
                font.pixelSize: 16
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                // TODO: Implement attachment functionality
                console.log("Attachment clicked")
            }
        }
        
        // Text input area
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
            ScrollBar.vertical.policy: ScrollBar.AsNeeded
            
            TextArea {
                id: textInput
                
                color: Colors.primaryText
                font.family: Typography.primaryFont
                font.pixelSize: Typography.bodyMedium
                
                wrapMode: TextArea.Wrap
                selectByMouse: true
                
                background: null
                
                // Remove default padding
                topPadding: 0
                bottomPadding: 0
                leftPadding: 0
                rightPadding: 0
                
                // Handle Enter key
                Keys.onPressed: function(event) {
                    if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                        if (event.modifiers & Qt.ShiftModifier) {
                            // Shift+Enter: new line
                            return
                        } else {
                            // Enter: send message
                            event.accepted = true
                            if (root.canSend) {
                                root.sendRequested()
                            }
                        }
                    }
                }
                
                onTextChanged: root.inputTextChanged()
            }
            
            // Placeholder text
            Text {
                id: placeholder
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                
                text: "Enter a message here, press ⏎ to send"
                color: Colors.placeholderText
                font: textInput.font
                
                visible: textInput.text.length === 0 && !textInput.activeFocus
            }
        }
        
        // Voice input button (placeholder)
        Button {
            Layout.preferredWidth: 32
            Layout.preferredHeight: 32
            
            background: Rectangle {
                color: parent.hovered ? Colors.hoverBackground : "transparent"
                radius: 6
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
            
            contentItem: Text {
                text: "🎤"
                font.pixelSize: 16
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                // TODO: Implement voice input functionality
                console.log("Voice input clicked")
            }
        }
        
        // Send button
        Button {
            Layout.preferredWidth: 32
            Layout.preferredHeight: 32
            
            enabled: root.canSend
            
            background: Rectangle {
                color: {
                    if (!parent.enabled) return Colors.mutedText
                    if (parent.hovered) return Colors.primaryHover
                    return Colors.primary
                }
                radius: 6
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
            
            contentItem: Text {
                text: "➤"
                color: parent.enabled ? Colors.primaryText : Colors.placeholderText
                font.pixelSize: 16
                font.weight: Typography.bold
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
            
            onClicked: {
                if (root.canSend) {
                    root.sendRequested()
                }
            }
        }
    }
}
