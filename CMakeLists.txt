cmake_minimum_required(VERSION 3.16)

project(ChamberUI VERSION 0.1 LANGUAGES CXX)

# 启用 QML 类型生成
set_property(SOURCE *.hh PROPERTY SKIP_AUTOGEN ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
find_package(Qt6 COMPONENTS Core Quick Qml Widgets Sql Network Multimedia TextToSpeech REQUIRED)

include(FetchContent)

FetchContent_Declare(
        gsl
        GIT_REPOSITORY https://github.com/microsoft/GSL.git
        GIT_TAG v4.0.0
)
FetchContent_Declare(
        magic_enum
        GIT_REPOSITORY https://github.com/Neargye/magic_enum.git
        GIT_TAG v0.9.5  # 你可以选择你需要的具体版本
)


FetchContent_MakeAvailable(gsl)
FetchContent_MakeAvailable(magic_enum)

set(PROJECT_SOURCES
        src/main.cc
        src/models/ChatMessage.cpp
        src/models/ChatSession.cpp
        src/services/chat_context.cc
        src/services/session_config.cc
        src/services/llm_provider_base.cc
        src/services/openai_provider.cc
        src/viewmodels/ChatViewModel.cpp
        src/viewmodels/SessionListViewModel.cpp
        qml.qrc
)

qt_add_executable(ChamberUI
        ${PROJECT_SOURCES}
)

# Disable QML caching to work around build issues
# set(QT_QML_SKIP_CACHE_GENERATION TRUE)

# Make sure QML files are copied to the build directory
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/qml DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# For macOS, also copy QML files to the Resources directory in the app bundle
if (APPLE)
    add_custom_command(TARGET ChamberUI POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${CMAKE_CURRENT_SOURCE_DIR}/qml
            $<TARGET_FILE_DIR:ChamberUI>/../Resources/qml
    )
endif ()

# Add QML resources directly to the executable
qt_add_resources(ChamberUI "qml_resources"
        PREFIX "/"
        FILES
        qml/main.qml
        qml/components/ChatView.qml
        qml/components/SessionList.qml
        qml/components/MessageBubble.qml
        qml/components/InputArea.qml
        qml/styles/Colors.qml
        qml/styles/Typography.qml
)

set_target_properties(ChamberUI PROPERTIES
        MACOSX_BUNDLE_GUI_IDENTIFIER com.soloholic.chamberui
        MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
        MACOSX_BUNDLE_BUNDLE_NAME "ChamberUI"
        MACOSX_BUNDLE_COPYRIGHT "© 2025 Soloholic. All rights reserved."
        MACOSX_BUNDLE_INFO_STRING "ChamberUI - LLM Chat Application"
        MACOSX_BUNDLE TRUE
        WIN32_EXECUTABLE TRUE
)

# Disable icon for now
if (APPLE)
    # We're not using an icon file for now
    # This comment prevents the warning about missing icon
endif ()

# 添加测试目录 (可选)
option(BUILD_TESTS "Build tests" OFF)
if (BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif ()

target_link_libraries(ChamberUI
        PRIVATE
        Qt6::Core
        Qt6::Quick
        Microsoft.GSL::GSL
)

install(TARGETS ChamberUI
        BUNDLE DESTINATION .
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR})
