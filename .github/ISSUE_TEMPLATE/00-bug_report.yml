name: Bug Report
description: Let us know that something does not work as expected.
title: "[Bug]: Please title this bug report"
body:
  - type: textarea
    id: what-happened
    attributes:
      label: Describe the issue
      description: What happened, and what did you expect to happen?
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce the problem
      description: It is important that we are able to reproduce the problem that you are experiencing. Please provide all code and relevant steps to reproduce the problem, including your `BUILD`/`CMakeLists.txt` file and build commands. Links to a GitHub branch or [godbolt.org](https://godbolt.org/) that demonstrate the problem are also helpful.
    validations:
      required: true
  - type: textarea
    id: version
    attributes:
      label: What version of GoogleTest are you using?
      description: Please include the output of `git rev-parse HEAD` or the GoogleTest release version number that you are using.
    validations:
      required: true
  - type: textarea
    id: os
    attributes:
      label: What operating system and version are you using?
      description: If you are using a Linux distribution please include the name and version of the distribution as well.
    validations:
      required: true
  - type: textarea
    id: compiler
    attributes:
      label: What compiler and version are you using?
      description: Please include the output of `gcc -v` or `clang -v`, or the equivalent for your compiler.
    validations:
      required: true
  - type: textarea
    id: buildsystem
    attributes:
      label: What build system are you using?
      description: Please include the output of `bazel --version` or `cmake --version`, or the equivalent for your build system.
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
    validations:
      required: false
