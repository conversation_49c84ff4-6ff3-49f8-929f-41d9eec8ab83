# ChamberUI - LLM Chat Assistant

一个基于Qt6和QML的现代化LLM聊天机器人应用，采用MVVM架构设计。

## 功能特性

- 🎨 **现代化UI设计** - 仿照Google Gemini的深色主题界面
- 🏗️ **MVVM架构** - 清晰的模型-视图-视图模型分离
- ⚡ **Qt6 + QML** - 使用最新的Qt6技术栈和QML声明式UI
- 🔧 **现代C++** - 使用C++23标准和QProperty绑定系统
- 💬 **多会话管理** - 支持创建和管理多个聊天会话
- 🔍 **会话搜索** - 快速搜索和过滤聊天历史
- 📱 **响应式布局** - 自适应不同窗口大小

## 项目结构

```
ChamberUI3/
├── src/
│   ├── main.cc                          # 应用程序入口
│   ├── models/                          # 数据模型
│   │   ├── ChatMessage.h/cpp           # 聊天消息模型
│   │   └── ChatSession.h/cpp           # 聊天会话模型
│   └── viewmodels/                      # 视图模型
│       ├── ChatViewModel.h/cpp         # 聊天视图模型
│       └── SessionListViewModel.h/cpp  # 会话列表视图模型
├── qml/
│   ├── main.qml                        # 主窗口
│   ├── components/                     # QML组件
│   │   ├── ChatView.qml               # 聊天视图
│   │   ├── SessionList.qml            # 会话列表
│   │   ├── MessageBubble.qml          # 消息气泡
│   │   └── InputArea.qml              # 输入区域
│   └── styles/                        # 样式定义
│       ├── Colors.qml                 # 颜色主题
│       ├── Typography.qml             # 字体样式
│       └── qmldir                     # QML模块定义
├── asserts/                           # 资源文件
└── CMakeLists.txt                     # 构建配置
```

## 技术栈

- **Qt6** - 跨平台应用程序框架
- **QML** - 声明式用户界面语言
- **C++23** - 现代C++标准
- **QProperty** - Qt6的属性绑定系统
- **CMake** - 构建系统

## 构建和运行

### 前置要求

- Qt6 (6.2或更高版本)
- CMake (3.16或更高版本)
- C++23兼容的编译器

### 构建步骤

1. 克隆项目
```bash
git clone <repository-url>
cd ChamberUI3
```

2. 创建构建目录
```bash
mkdir build
cd build
```

3. 配置和构建
```bash
cmake ..
cmake --build .
```

4. 运行应用程序
```bash
# macOS
./ChamberUI.app/Contents/MacOS/ChamberUI

# Linux/Windows
./ChamberUI
```

## 架构说明

### MVVM模式

- **Model (模型)**: `ChatMessage` 和 `ChatSession` 类负责数据存储和业务逻辑
- **View (视图)**: QML文件定义用户界面和交互
- **ViewModel (视图模型)**: `ChatViewModel` 和 `SessionListViewModel` 处理UI逻辑和数据绑定

### 关键特性

1. **QProperty绑定**: 使用Qt6的现代属性系统实现自动UI更新
2. **组件化设计**: 可重用的QML组件提高代码复用性
3. **样式系统**: 集中的颜色和字体管理
4. **类型安全**: 强类型的C++后端确保运行时安全

## 使用说明

1. **创建新会话**: 点击左侧面板的"New Chat"按钮
2. **发送消息**: 在底部输入框中输入消息，按Enter发送
3. **切换会话**: 点击左侧会话列表中的任意会话
4. **搜索会话**: 使用左侧顶部的搜索框过滤会话

## 最近更新

### v0.1.1 - UI布局优化
- ✅ 修复了消息气泡宽度过窄的问题
- ✅ 解决了消息区域与标题栏重叠的问题
- ✅ 优化了消息气泡的大小计算逻辑
- ✅ 改进了左侧边栏的宽度设置
- ✅ 修复了TextArea的绑定循环问题
- ✅ 提升了整体UI的响应性和可用性

## 开发计划

- [ ] 集成真实的LLM API (OpenAI, Claude等)
- [ ] 添加消息导出功能
- [ ] 支持文件上传和附件
- [ ] 添加语音输入功能
- [ ] 实现消息搜索
- [ ] 添加主题切换
- [ ] 支持插件系统
- [ ] 优化消息渲染性能
- [ ] 添加消息编辑和删除功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
