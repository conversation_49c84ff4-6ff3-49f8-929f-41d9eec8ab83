# Googletest Samples

If you're like us, you'd like to look at
[googletest samples.](https://github.com/google/googletest/blob/main/googletest/samples)
The sample directory has a number of well-commented samples showing how to use a
variety of googletest features.

*   Sample #1 shows the basic steps of using googletest to test C++ functions.
*   Sample #2 shows a more complex unit test for a class with multiple member
    functions.
*   Sample #3 uses a test fixture.
*   Sample #4 teaches you how to use googletest and `googletest.h` together to
    get the best of both libraries.
*   Sample #5 puts shared testing logic in a base test fixture, and reuses it in
    derived fixtures.
*   Sample #6 demonstrates type-parameterized tests.
*   Sample #7 teaches the basics of value-parameterized tests.
*   Sample #8 shows using `Combine()` in value-parameterized tests.
*   Sample #9 shows use of the listener API to modify Google Test's console
    output and the use of its reflection API to inspect test results.
*   Sample #10 shows use of the listener API to implement a primitive memory
    leak checker.
