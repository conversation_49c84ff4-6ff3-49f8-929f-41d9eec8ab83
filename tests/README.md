# ChamberUI Tests

This directory contains unit tests for the ChamberUI project, specifically comprehensive tests for the OpenAI provider service.

## Test Structure

### OpenAI Provider Tests (`openai_provider_test.cpp`)

The test suite provides comprehensive coverage for the `OpenAIProvider` class with the following test categories:

#### 1. Authorization Tests
- `SetAuthorizationSetsCorrectHeader`: Verifies correct Bearer token format
- `SetAuthorizationWithEmptyApiKey`: Tests behavior with empty API key
- `SetAuthorizationWithDifferentConfigs`: Tests various API key formats including special characters and Unicode

#### 2. Request Body Tests
- `InitRequestBodyCreatesEmptyObject`: Verifies the current implementation behavior

#### 3. Stream Processing Tests
- **Valid JSON Processing**:
  - `OnStreamLineWithValidContent`: Tests normal content processing
  - `OnStreamLineWithEmptyContent`: Tests empty content handling
  - `OnStreamLineWithUnicodeContent`: Tests Unicode character support
  - `OnStreamLineWithSpecialCharacters`: Tests special characters and escape sequences

- **Error Handling**:
  - `OnStreamLineWithInvalidJSON`: Tests malformed JSON handling
  - `OnStreamLineWithMalformedJSON`: Tests incomplete JSON structures
  - `OnStreamLineWithNullContent`: Tests null content values
  - `OnStreamLineWithNonStringContent`: Tests non-string content types

- **Edge Cases**:
  - `OnStreamLineWithDoneMessage`: Tests [DONE] message handling
  - `OnStreamLineWithDoneMessageTrimmed`: Tests trimmed [DONE] messages
  - `OnStreamLineWithNoChoicesArray`: Tests missing choices array
  - `OnStreamLineWithEmptyChoicesArray`: Tests empty choices array
  - `OnStreamLineWithNoDeltaObject`: Tests missing delta object
  - `OnStreamLineWithDeltaButNoContent`: Tests delta without content

#### 4. Stream Fragment Tests
- `OnStreamFragmentWithSingleLine`: Tests single data line processing
- `OnStreamFragmentWithMultipleLines`: Tests multiple data lines in sequence
- `OnStreamFragmentWithEmptyData`: Tests empty data handling
- `OnStreamFragmentWithNonDataLines`: Tests non-data lines filtering
- `OnStreamFragmentWithMixedLines`: Tests mixed data and non-data lines
- `OnStreamFragmentWithMalformedDataPrefix`: Tests malformed data prefixes
- `OnStreamFragmentWithInvalidJSONInDataLine`: Tests invalid JSON in data lines

#### 5. Performance Tests
- `OnStreamFragmentPerformanceWithManySmallChunks`: Tests performance with 1000 small chunks
- `OnStreamFragmentWithLargeData`: Tests handling of large data volumes
- `OnStreamLineWithVeryLongContent`: Tests very long content strings (10k characters)

#### 6. Integration Tests
- `SendMessageCreatesCorrectRequest`: Basic integration test for the main API

#### 7. Thread Safety Tests
- `OnStreamLineThreadSafety`: Basic thread safety verification

## Test Helpers

### `test_helpers.h/cpp`
Provides essential testing utilities:

- **MockLLMMessageHandler**: Google Mock implementation of `LLMMessageHandler`
- **MockNetworkReply**: Mock `QNetworkReply` for network testing
- **OpenAIProviderTestBase**: Base test fixture with common setup
- **Helper Functions**:
  - `createOpenAIStreamResponse()`: Creates valid OpenAI API responses
  - `createOpenAIErrorResponse()`: Creates error responses
  - `createMultiLineStreamData()`: Creates multi-line stream data

## Building and Running Tests

### Prerequisites
- CMake 3.16+
- Qt6 (Core, Network, Test)
- Google Test (automatically fetched)
- Microsoft GSL (automatically fetched)

### Build Configuration
```bash
# Configure with tests enabled
cmake -DBUILD_TESTS=ON -B build

# Build the tests
cmake --build build --target openai_provider_tests

# Run the tests
./build/tests/openai_provider_tests
```

### Alternative: Using CTest
```bash
# Run all tests through CTest
cd build
ctest --verbose
```

## Test Coverage

The test suite covers:
- ✅ All public and protected methods of `OpenAIProvider`
- ✅ Error handling for malformed JSON and network issues
- ✅ Unicode and special character support
- ✅ Performance with large data sets
- ✅ Edge cases and boundary conditions
- ✅ Integration with the base class functionality

## Adding New Tests

When adding new tests:

1. Follow the existing naming convention: `MethodName_Scenario_ExpectedBehavior`
2. Use the `EXPECT_CALL` macro for mock expectations
3. Include both positive and negative test cases
4. Add performance tests for methods that process large amounts of data
5. Test Unicode and special character handling where applicable

## Mock Objects

The test suite uses Google Mock for creating test doubles:
- `MockLLMMessageHandler`: Mocks the message handler interface
- `MockNetworkReply`: Mocks Qt's network reply for testing network interactions

## Test Data

Test data is generated programmatically using helper functions to ensure consistency and maintainability. The helpers create valid OpenAI API response formats for testing various scenarios.
