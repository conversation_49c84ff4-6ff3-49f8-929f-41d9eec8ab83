cmake_minimum_required(VERSION 3.16)

project(ChamberUITests CXX)

# Enable testing
enable_testing()

# Find required packages
find_package(Qt6 COMPONENTS Core Network Test REQUIRED)

# Fetch GoogleTest
include(FetchContent)
FetchContent_Declare(
  googletest
  GIT_REPOSITORY https://github.com/google/googletest.git
  GIT_TAG v1.14.0
)
FetchContent_MakeAvailable(googletest)

# Include GSL (already available from parent)
FetchContent_Declare(
        gsl
        GIT_REPOSITORY https://github.com/microsoft/GSL.git
        GIT_TAG v4.0.0
)
FetchContent_MakeAvailable(gsl)

# Set C++ standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)

# Create a library with the source files we want to test
add_library(ChamberUILib STATIC
    ../src/services/chat_context.cc
    ../src/services/session_config.cc
    ../src/services/llm_provider_base.cc
    ../src/services/openai_provider.cc
)

target_include_directories(ChamberUILib PUBLIC
    ../src
    ../src/services
    ../src/models
)

target_link_libraries(ChamberUILib
    Qt6::Core
    Qt6::Network
    Microsoft.GSL::GSL
)

# Test executable
add_executable(openai_provider_tests
    openai_provider_test.cpp
    test_helpers.cpp
)

target_include_directories(openai_provider_tests PRIVATE
    ../src
    ../src/services
    ../src/models
)

target_link_libraries(openai_provider_tests
    ChamberUILib
    Qt6::Core
    Qt6::Network
    Qt6::Test
    Microsoft.GSL::GSL
    gtest
    gtest_main
    gmock
    gmock_main
)

# Add test to CTest
add_test(NAME OpenAIProviderTests COMMAND openai_provider_tests)

# Set test properties
set_target_properties(openai_provider_tests PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)
