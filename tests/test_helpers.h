#pragma once

#include <QObject>
#include <QString>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QNetworkAccessManager>
#include <QSignalSpy>
#include <QTimer>
#include <QEventLoop>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "chat_context.h"
#include "message_handler.h"
#include "session_config.h"

namespace ChamberUI::Services::Test {

// Mock LLMMessageHandler for testing
class MockLLMMessageHandler : public LLMMessageHandler {
public:
    MOCK_METHOD(void, onStreamFragment, (const QString& message, const ChatContext& context), (override));
    MOCK_METHOD(void, onError, (const QString& error, const ChatContext& context), (override));
    MOCK_METHOD(void, onFinished, (bool succeed, const ChatContext& context), (override));
};

// Mock QNetworkReply for testing network responses
class MockNetworkReply : public QNetworkReply {
    Q_OBJECT

public:
    explicit MockNetworkReply(QObject* parent = nullptr);
    
    // QNetworkReply interface
    void abort() override;
    qint64 bytesAvailable() const override;
    bool isSequential() const override;
    
    // Test helpers
    void setError(NetworkError error, const QString& errorString);
    void emitReadyRead();
    void emitFinished();
    void setResponseData(const QByteArray& data);
    void appendResponseData(const QByteArray& data);

protected:
    qint64 readData(char* data, qint64 maxlen) override;

private:
    QByteArray m_responseData;
    qint64 m_readPosition = 0;
};

// Test fixture base class
class OpenAIProviderTestBase : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Helper methods
    SessionConfig createTestConfig();
    ChatContext createTestContext();
    
    // Test data
    std::unique_ptr<QObject> m_parent;
};

// Helper functions
QString createOpenAIStreamResponse(const QString& content, bool isDone = false);
QString createOpenAIErrorResponse(const QString& error);
QByteArray createMultiLineStreamData(const QStringList& contents);

} // namespace ChamberUI::Services::Test
