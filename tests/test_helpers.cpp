#include "test_helpers.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

namespace ChamberUI::Services::Test {

// MockNetworkReply implementation
MockNetworkReply::MockNetworkReply(QObject* parent) 
    : QNetworkReply(parent) {
    setOpenMode(QIODevice::ReadOnly);
}

void MockNetworkReply::abort() {
    // Mock implementation - do nothing
}

qint64 MockNetworkReply::bytesAvailable() const {
    return m_responseData.size() - m_readPosition + QNetworkReply::bytesAvailable();
}

bool MockNetworkReply::isSequential() const {
    return true;
}

void MockNetworkReply::setError(NetworkError error, const QString& errorString) {
    QNetworkReply::setError(error, errorString);
}

void MockNetworkReply::emitReadyRead() {
    emit readyRead();
}

void MockNetworkReply::emitFinished() {
    emit finished();
}

void MockNetworkReply::setResponseData(const QByteArray& data) {
    m_responseData = data;
    m_readPosition = 0;
}

void MockNetworkReply::appendResponseData(const QByteArray& data) {
    m_responseData.append(data);
}

qint64 MockNetworkReply::readData(char* data, qint64 maxlen) {
    qint64 available = m_responseData.size() - m_readPosition;
    qint64 toRead = qMin(maxlen, available);
    
    if (toRead > 0) {
        memcpy(data, m_responseData.constData() + m_readPosition, toRead);
        m_readPosition += toRead;
    }
    
    return toRead;
}

// OpenAIProviderTestBase implementation
void OpenAIProviderTestBase::SetUp() {
    m_parent = std::make_unique<QObject>();
}

void OpenAIProviderTestBase::TearDown() {
    m_parent.reset();
}

SessionConfig OpenAIProviderTestBase::createTestConfig() {
    SessionConfig config;
    config.api_url = "https://api.openai.com/v1/chat/completions";
    config.api_key = "test-api-key";
    config.model = "gpt-3.5-turbo";
    config.temperature = 0.7;
    config.max_tokens = 1000;
    config.reply_language = "en";
    return config;
}

ChatContext OpenAIProviderTestBase::createTestContext() {
    auto config = createTestConfig();
    ChatContext context(config);
    context.addMessage("Hello", MessageRole::User);
    return context;
}

// Helper functions
QString createOpenAIStreamResponse(const QString& content, bool isDone) {
    if (isDone) {
        return "[DONE]";
    }
    
    QJsonObject delta;
    delta["content"] = content;
    
    QJsonObject choice;
    choice["delta"] = delta;
    choice["index"] = 0;
    choice["finish_reason"] = QJsonValue::Null;
    
    QJsonArray choices;
    choices.append(choice);
    
    QJsonObject response;
    response["id"] = "chatcmpl-test";
    response["object"] = "chat.completion.chunk";
    response["created"] = **********;
    response["model"] = "gpt-3.5-turbo";
    response["choices"] = choices;
    
    return QJsonDocument(response).toJson(QJsonDocument::Compact);
}

QString createOpenAIErrorResponse(const QString& error) {
    QJsonObject errorObj;
    errorObj["message"] = error;
    errorObj["type"] = "invalid_request_error";
    
    QJsonObject response;
    response["error"] = errorObj;
    
    return QJsonDocument(response).toJson(QJsonDocument::Compact);
}

QByteArray createMultiLineStreamData(const QStringList& contents) {
    QByteArray result;
    for (const QString& content : contents) {
        result += "data: " + createOpenAIStreamResponse(content).toUtf8() + "\n";
    }
    result += "data: " + createOpenAIStreamResponse("", true).toUtf8() + "\n";
    return result;
}

} // namespace ChamberUI::Services::Test

// Remove moc include since MockNetworkReply doesn't need it in this context
