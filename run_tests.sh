#!/bin/bash

# ChamberUI Test Runner Script
# This script builds and runs the OpenAI Provider unit tests

set -e  # Exit on any error

echo "🧪 ChamberUI Test Runner"
echo "======================="

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Create build directory if it doesn't exist
BUILD_DIR="build"
if [ ! -d "$BUILD_DIR" ]; then
    echo "📁 Creating build directory..."
    mkdir -p "$BUILD_DIR"
fi

echo "🔧 Configuring CMake with tests enabled..."
cmake -DBUILD_TESTS=ON -B "$BUILD_DIR" -DCMAKE_BUILD_TYPE=Debug

echo "🔨 Building tests..."
cmake --build "$BUILD_DIR" --target openai_provider_tests --parallel

echo "🏃 Running OpenAI Provider tests..."
echo "=================================="

# Run the tests
if [ -f "$BUILD_DIR/tests/openai_provider_tests" ]; then
    "$BUILD_DIR/tests/openai_provider_tests" --gtest_color=yes
    TEST_RESULT=$?
else
    echo "❌ Error: Test executable not found at $BUILD_DIR/tests/openai_provider_tests"
    exit 1
fi

echo ""
echo "=================================="
if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "❌ Some tests failed (exit code: $TEST_RESULT)"
fi

echo ""
echo "📊 To run tests with CTest:"
echo "   cd $BUILD_DIR && ctest --verbose"
echo ""
echo "🔍 To run specific test patterns:"
echo "   $BUILD_DIR/tests/openai_provider_tests --gtest_filter='*StreamLine*'"
echo ""
echo "📈 To run tests with coverage (if available):"
echo "   cmake -DBUILD_TESTS=ON -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=ON -B $BUILD_DIR"

exit $TEST_RESULT
